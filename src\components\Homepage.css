/* Homepage Component Styles - Responsive untuk tablet 11 inch landscape */

/* Android/Web Font Consistency System */
.homepage-container {
  /* Ensure proper text color contrast for Android visibility */
  color: oklch(var(--bc)) !important;
  /* Enhanced font rendering for Android */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "kern" 1;
  font-kerning: normal;
}

/* Override any white text on white background issues */
.homepage-container * {
  color: inherit;
  /* Ensure consistent font weight rendering */
  font-weight: inherit;
  font-family: inherit;
}

/* Ensure proper contrast for all text elements */
.text-base-content {
  color: oklch(var(--bc)) !important;
  /* Force color inheritance for Android WebView */
  -webkit-text-fill-color: oklch(var(--bc)) !important;
}

.text-base-content\/70 {
  color: oklch(var(--bc) / 0.7) !important;
  -webkit-text-fill-color: oklch(var(--bc) / 0.7) !important;
}

.text-base-content\/80 {
  color: oklch(var(--bc) / 0.8) !important;
  -webkit-text-fill-color: oklch(var(--bc) / 0.8) !important;
}

/* Android-specific font fixes */
@media screen and (-webkit-min-device-pixel-ratio: 1) {
  body, .homepage-container {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif !important;
  }

  /* Ensure proper font weight rendering on Android */
  .font-semibold {
    font-weight: 600 !important;
  }

  .font-bold {
    font-weight: 700 !important;
  }

  .font-medium {
    font-weight: 500 !important;
  }
}

/* Date/Time display specific styles */
.datetime-display {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.datetime-time {
  font-family: 'Courier New', monospace;
  letter-spacing: 0.1em;
}

.datetime-date {
  font-weight: 500;
  line-height: 1.4;
}

/* Consistent Gradient Background System */
.gradient-bg-primary {
  background: linear-gradient(135deg,
    oklch(var(--p) / 0.15) 0%,
    oklch(var(--s) / 0.1) 50%,
    oklch(var(--a) / 0.05) 100%);
}

.gradient-bg-secondary {
  background: linear-gradient(45deg,
    oklch(var(--s) / 0.1) 0%,
    oklch(var(--p) / 0.08) 50%,
    oklch(var(--a) / 0.12) 100%);
}

.gradient-bg-accent {
  background: linear-gradient(225deg,
    oklch(var(--a) / 0.1) 0%,
    oklch(var(--p) / 0.05) 50%,
    oklch(var(--s) / 0.08) 100%);
}

/* Enhanced gradient for main backgrounds */
.bg-gradient-to-br {
  position: relative;
  background: linear-gradient(135deg,
    oklch(var(--p) / 0.12) 0%,
    oklch(var(--s) / 0.08) 35%,
    oklch(var(--a) / 0.06) 70%,
    oklch(var(--p) / 0.04) 100%);
}

.bg-gradient-to-br::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%,
    oklch(var(--p) / 0.08) 0%,
    transparent 50%),
    radial-gradient(circle at 70% 80%,
    oklch(var(--s) / 0.06) 0%,
    transparent 50%);
  z-index: 0;
}

.bg-gradient-to-br > * {
  position: relative;
  z-index: 1;
}

/* CSS Variables untuk animasi carousel */
:root {
  --item1-transform: translateX(-100%) translateY(-5%) scale(1.5);
  --item1-filter: blur(30px);
  --item1-zIndex: 11;
  --item1-opacity: 0;

  --item2-transform: translateX(0);
  --item2-filter: blur(0px);
  --item2-zIndex: 10;
  --item2-opacity: 1;

  --item3-transform: translate(50%, 10%) scale(0.8);
  --item3-filter: blur(10px);
  --item3-zIndex: 9;
  --item3-opacity: 1;

  --item4-transform: translate(90%, 20%) scale(0.5);
  --item4-filter: blur(30px);
  --item4-zIndex: 8;
  --item4-opacity: 1;

  --item5-transform: translate(120%, 30%) scale(0.3);
  --item5-filter: blur(40px);
  --item5-zIndex: 7;
  --item5-opacity: 0;
}

/* Container utama homepage */
.homepage-container {
  width: 100%;
  height: 100%;
  font-family: 'Poppins', sans-serif;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Header styling */
.header {
  width: 1140px;
  max-width: 90%;
  display: flex;
  justify-content: flex-start;
  margin: auto;
  height: 60px;
  align-items: center;
  padding: 0 20px;
  flex-shrink: 0;
}

.company-header {
  display: flex;
  align-items: center;
  gap: 15px;
}

.company-logo {
  width: 40px;
  height: 40px;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(105, 62, 255, 0.2);
}

.company-name {
  font-weight: 600;
  font-size: 1.4rem;
  color: #693EFF;
  letter-spacing: 0.5px;
}

/* Removed divider styling - using gradient background system instead */

/* Carousel styling */
.carousel {
  position: relative;
  flex: 1;
  overflow: hidden;
  margin-top: -50px;
  min-height: 0;
}

/* Removed carousel background styling for cleaner look */

.carousel-list {
  position: absolute;
  width: 1140px;
  max-width: 90%;
  height: 80%;
  left: 50%;
  transform: translateX(-50%);
}

.carousel-item {
  position: absolute;
  left: 0%;
  width: 70%;
  height: 100%;
  font-size: 15px;
  transition: left 0.5s, opacity 0.5s, width 0.5s;
}

.carousel-item:nth-child(n + 6) {
  opacity: 0;
}

.carousel-item:nth-child(2) {
  z-index: 10;
  transform: translateX(0);
}

.product-image {
  width: 50%;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  transition: right 1.5s;
  object-fit: contain;
}

/* Introduce section styling */
.introduce {
  opacity: 0;
  pointer-events: none;
}

.carousel-item:nth-child(2) .introduce {
  opacity: 1;
  pointer-events: auto;
  width: 400px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  transition: opacity 0.5s;
}

.title {
  font-size: 2em;
  font-weight: 500;
  line-height: 1em;
  color: #555;
}

.topic {
  font-size: 4em;
  font-weight: 500;
  color: #333;
  margin: 10px 0;
}

.description {
  font-size: small;
  color: #5559;
  margin: 20px 0;
  line-height: 1.6;
}

.see-more-btn {
  font-family: 'Poppins', sans-serif;
  margin-top: 1.2em;
  padding: 5px 0;
  border: none;
  border-bottom: 1px solid #555;
  background-color: transparent;
  font-weight: bold;
  letter-spacing: 3px;
  transition: background 0.5s;
  cursor: pointer;
}

.see-more-btn:hover {
  background: #eee;
  padding: 5px 10px;
}

/* Detail section styling */
.detail {
  opacity: 0;
  pointer-events: none;
}

.detail-title {
  font-size: 4em;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
}

.detail-description {
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 30px;
}

.specifications {
  display: flex;
  gap: 10px;
  width: 100%;
  border-top: 1px solid #5553;
  margin-top: 20px;
  padding-top: 20px;
  flex-wrap: wrap;
}

.spec-item {
  width: 90px;
  text-align: center;
  flex-shrink: 0;
}

.spec-item p:nth-child(1) {
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.spec-item p:nth-child(2) {
  color: #666;
  font-size: 0.9rem;
}

.checkout {
  margin-top: 30px;
  display: flex;
  gap: 10px;
}

.checkout button {
  font-family: 'Poppins', sans-serif;
  padding: 10px 20px;
  letter-spacing: 2px;
  font-weight: 500;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
}

/* Positioning untuk item carousel */
.carousel-item:nth-child(1) {
  transform: var(--item1-transform);
  filter: var(--item1-filter);
  z-index: var(--item1-zIndex);
  opacity: var(--item1-opacity);
  pointer-events: none;
}

.carousel-item:nth-child(3) {
  transform: var(--item3-transform);
  filter: var(--item3-filter);
  z-index: var(--item3-zIndex);
}

.carousel-item:nth-child(4) {
  transform: var(--item4-transform);
  filter: var(--item4-filter);
  z-index: var(--item4-zIndex);
}

.carousel-item:nth-child(5) {
  transform: var(--item5-transform);
  filter: var(--item5-filter);
  opacity: var(--item5-opacity);
  pointer-events: none;
}

/* Animasi untuk konten item aktif */
.carousel-item:nth-child(2) .title,
.carousel-item:nth-child(2) .topic,
.carousel-item:nth-child(2) .description,
.carousel-item:nth-child(2) .see-more-btn {
  opacity: 0;
  animation: showContent 0.5s 1s ease-in-out 1 forwards;
}

@keyframes showContent {
  from {
    transform: translateY(-30px);
    filter: blur(10px);
  }
  to {
    transform: translateY(0);
    opacity: 1;
    filter: blur(0px);
  }
}

.carousel-item:nth-child(2) .topic {
  animation-delay: 1.2s;
}

.carousel-item:nth-child(2) .description {
  animation-delay: 1.4s;
}

.carousel-item:nth-child(2) .see-more-btn {
  animation-delay: 1.6s;
}

/* Navigation arrows */
.arrows {
  position: absolute;
  bottom: 10px;
  width: 1140px;
  max-width: 90%;
  display: flex;
  justify-content: space-between;
  left: 50%;
  transform: translateX(-50%);
}

.nav-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-family: monospace;
  border: 1px solid #5555;
  font-size: large;
  background-color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-btn:hover {
  background-color: #693EFF;
  color: white;
  transform: scale(1.1);
}

.nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.back-btn {
  position: absolute;
  z-index: 100;
  bottom: 0%;
  left: 50%;
  transform: translateX(-50%);
  border: none;
  border-bottom: 1px solid #555;
  font-family: 'Poppins', sans-serif;
  font-weight: bold;
  letter-spacing: 3px;
  background-color: transparent;
  padding: 10px;
  opacity: 0;
  transition: opacity 0.5s;
  cursor: pointer;
}

.back-btn.visible {
  opacity: 1;
}

.back-btn:hover {
  background: #eee;
  padding: 10px 15px;
}

/* Animasi untuk transisi next */
.carousel.next .carousel-item:nth-child(1) {
  animation: transformFromPosition2 0.5s ease-in-out 1 forwards;
}

@keyframes transformFromPosition2 {
  from {
    transform: var(--item2-transform);
    filter: var(--item2-filter);
    opacity: var(--item2-opacity);
  }
}

.carousel.next .carousel-item:nth-child(2) {
  animation: transformFromPosition3 0.7s ease-in-out 1 forwards;
}

@keyframes transformFromPosition3 {
  from {
    transform: var(--item3-transform);
    filter: var(--item3-filter);
    opacity: var(--item3-opacity);
  }
}

.carousel.next .carousel-item:nth-child(3) {
  animation: transformFromPosition4 0.9s ease-in-out 1 forwards;
}

@keyframes transformFromPosition4 {
  from {
    transform: var(--item4-transform);
    filter: var(--item4-filter);
    opacity: var(--item4-opacity);
  }
}

.carousel.next .carousel-item:nth-child(4) {
  animation: transformFromPosition5 1.1s ease-in-out 1 forwards;
}

@keyframes transformFromPosition5 {
  from {
    transform: var(--item5-transform);
    filter: var(--item5-filter);
    opacity: var(--item5-opacity);
  }
}

/* Animasi untuk transisi previous */
.carousel.prev .carousel-item:nth-child(5) {
  animation: transformFromPosition4 0.5s ease-in-out 1 forwards;
}

.carousel.prev .carousel-item:nth-child(4) {
  animation: transformFromPosition3 0.7s ease-in-out 1 forwards;
}

.carousel.prev .carousel-item:nth-child(3) {
  animation: transformFromPosition2 0.9s ease-in-out 1 forwards;
}

.carousel.prev .carousel-item:nth-child(2) {
  animation: transformFromPosition1 1.1s ease-in-out 1 forwards;
}

@keyframes transformFromPosition1 {
  from {
    transform: var(--item1-transform);
    filter: var(--item1-filter);
    opacity: var(--item1-opacity);
  }
}

/* Show detail animations */
.carousel.showDetail .carousel-item:nth-child(3),
.carousel.showDetail .carousel-item:nth-child(4) {
  left: 100%;
  opacity: 0;
  pointer-events: none;
}

.carousel.showDetail .carousel-item:nth-child(2) {
  width: 100%;
}

.carousel.showDetail .carousel-item:nth-child(2) .introduce {
  opacity: 0;
  pointer-events: none;
}

.carousel.showDetail .carousel-item:nth-child(2) .product-image {
  right: 50%;
}

.carousel.showDetail .carousel-item:nth-child(2) .detail {
  opacity: 1;
  width: 50%;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  text-align: right;
  pointer-events: auto;
}

.carousel.showDetail .carousel-item:nth-child(2) .detail .detail-title,
.carousel.showDetail .carousel-item:nth-child(2) .detail .detail-description,
.carousel.showDetail .carousel-item:nth-child(2) .detail .specifications,
.carousel.showDetail .carousel-item:nth-child(2) .detail .checkout {
  opacity: 0;
  animation: showContent 0.5s 1s ease-in-out 1 forwards;
}

.carousel.showDetail .carousel-item:nth-child(2) .detail .detail-description {
  animation-delay: 1.2s;
}

.carousel.showDetail .carousel-item:nth-child(2) .detail .specifications {
  animation-delay: 1.4s;
}

.carousel.showDetail .carousel-item:nth-child(2) .detail .checkout {
  animation-delay: 1.6s;
}

.carousel.showDetail .nav-btn {
  opacity: 0;
  pointer-events: none;
}

/* Responsive Design untuk Tablet 11 inch Landscape (1366px) */
@media screen and (min-width: 1024px) and (max-width: 1366px) {
  .carousel-item {
    width: 80%;
  }

  .carousel-item:nth-child(2) .introduce {
    width: 350px;
  }

  .topic {
    font-size: 3.5em;
  }

  .detail-title {
    font-size: 3.5em;
  }

  .specifications {
    gap: 8px;
  }

  .spec-item {
    width: 80px;
  }

  /* Ensure proper layout for tablet landscape */
  .lg\:grid-cols-2 > div:first-child {
    border-right: 1px solid oklch(var(--b3));
  }
}

/* Tablet Portrait specific adjustments */
@media screen and (min-width: 768px) and (max-width: 1023px) and (orientation: portrait) {
  /* Ensure proper height distribution */
  .h-2\/5 {
    height: 40% !important;
  }

  .h-3\/5 {
    height: 60% !important;
  }
}

/* Responsive Design untuk Tablet Portrait dan iPad */
@media screen and (max-width: 1023px) {
  .carousel-item {
    width: 90%;
  }

  .carousel-item:nth-child(2) .introduce {
    width: 300px;
  }

  .topic {
    font-size: 3em;
  }

  .detail-title {
    font-size: 2.5em;
  }

  .carousel.showDetail .carousel-item:nth-child(2) .detail .specifications {
    overflow: auto;
  }

  .specifications {
    gap: 6px;
  }

  .spec-item {
    width: 70px;
    font-size: 0.85rem;
  }
}

/* Responsive Design untuk Mobile */
@media screen and (max-width: 767px) {
  .carousel-item {
    width: 100%;
    font-size: 10px;
  }

  .carousel-list {
    height: 100%;
  }

  .carousel-item:nth-child(2) .introduce {
    width: 50%;
  }

  .product-image {
    width: 40%;
  }

  .topic {
    font-size: 2.5em;
  }

  .title {
    font-size: 1.5em;
  }

  .description {
    height: 100px;
    overflow: auto;
  }

  .detail-title {
    font-size: 2em;
  }

  .carousel.showDetail .carousel-item:nth-child(2) .detail {
    backdrop-filter: blur(10px);
    font-size: small;
  }

  .carousel.showDetail .carousel-item:nth-child(2) .detail .detail-description {
    height: 100px;
    overflow: auto;
  }

  .carousel.showDetail .carousel-item:nth-child(2) .detail .checkout {
    display: flex;
    width: max-content;
    float: right;
  }

  .specifications {
    flex-direction: column;
    gap: 10px;
  }

  .spec-item {
    width: 100%;
    text-align: left;
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
    border-bottom: 1px solid #eee;
  }

  .header {
    padding: 0 15px;
    height: 60px;
  }

  .company-name {
    font-size: 1.1rem;
  }

  .company-logo {
    width: 35px;
    height: 35px;
  }
}
